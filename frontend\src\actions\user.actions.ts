"use server";

import { db } from "@/lib/db";

export async function getUser(uid: string) {
  const user = await db.user.findUnique({
    where: {
      id: uid,
    },
  });

  return user;
}

export async function getUserFromEntriesId(entryId: string) {
  const entry = await db.entries.findUnique({
    where: {
      id: entryId,
    },
  });

  if (!entry) {
    return null;
  }

  const user = await db.user.findUnique({
    where: {
      id: entry.userId,
    },
  });

  return user?.email;
}