@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.606 0.25 292.717);
  --primary-foreground: oklch(0.969 0.016 293.756);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.606 0.25 292.717);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.606 0.25 292.717);
  --sidebar-primary-foreground: oklch(0.969 0.016 293.756);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.606 0.25 292.717);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.541 0.281 293.009);
  --primary-foreground: oklch(0.969 0.016 293.756);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.541 0.281 293.009);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.541 0.281 293.009);
  --sidebar-primary-foreground: oklch(0.969 0.016 293.756);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.541 0.281 293.009);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}

.animate-marquee {
  animation: marquee var(--duration) linear infinite;
}

.animate-marquee-vertical {
  animation: marquee-vertical var(--duration) linear infinite;
}

canvas#neuro {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  opacity: 0.95;
  z-index: 0;
}

.glass-effect {
  backdrop-filter: blur(14px) brightness(0.91);
  -webkit-backdrop-filter: blur(14px) brightness(0.91);
}

.floating {
  animation: float 6s ease-in-out infinite;
}

.floating-delay {
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(0px);
  }
}

/* Card */

.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.connector {
  stroke-dasharray: 8;
  animation: dataStream 2s linear infinite;
}

.table-float {
  animation: float 6s ease-in-out infinite;
}

.table-float:nth-child(2) {
  animation-delay: -1s;
}

.table-float:nth-child(3) {
  animation-delay: -2s;
}

.table-float:nth-child(4) {
  animation-delay: -3s;
}

.gradient-border {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(179, 70, 229, 0.1),
    rgba(212, 59, 246, 0.1),
    rgba(139, 92, 246, 0.1)
  );
}

.gradient-border::before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, #d546e5, #8c3bf6, #8b5cf6, #f50bc2);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.inner-glow {
  box-shadow: inset 0 0 20px rgba(165, 70, 229, 0.3),
    inset 0 0 40px rgba(199, 59, 246, 0.2), 0 0 30px rgba(139, 92, 246, 0.4);
}

.card-border {
  background: rgba(79, 70, 229, 0.08);
  border: 1px solid rgba(144, 70, 229, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 0 0 1px rgba(179, 70, 229, 0.3),
    inset 0 0 30px rgba(147, 70, 229, 0.1),
    inset 0 0 60px rgba(199, 59, 246, 0.05), 0 0 50px rgba(139, 92, 246, 0.2);
}

.dark .neural-bg {
  background-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.08) 1px,
      transparent 1px
    ),
    linear-gradient(rgba(255, 255, 255, 0.08) 1px, transparent 1px) !important;
}

button[style*="position: absolute;"][style*="right: 0.6rem;"][style*="top: 0.6rem;"][style*="background-color: transparent;"] {
  display: none !important;
}

.purple-gradient {
  background: radial-gradient(
    circle,
    rgba(248, 249, 250, 1) 0,
    rgba(240, 242, 247, 1) 86%,
    rgba(156, 59, 258, 0.3) 100%
  );
}

.dark .purple-gradient {
  background: radial-gradient(
    circle,
    rgba(23, 23, 23, 0) 0,
    rgba(20, 17, 10, 0.2) 86%,
    rgba(104, 27, 289, 0.52) 100%
  );
}

.glass-border {
  border: 1px solid rgba(194, 194, 194, 0.11);
  background: rgba(194, 194, 194, 0.12);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.dark .glass-border {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.glass-border:focus-within {
  border-color: rgba(139, 92, 246, 0.4);
  background: rgba(139, 92, 246, 0.05);
}

.custom-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  position: relative;
  cursor: pointer;
}

.dark .custom-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  position: relative;
  cursor: pointer;
}

.custom-checkbox:checked {
  background: rgb(139, 92, 246);
  border-color: rgb(139, 92, 246);
}

.custom-checkbox:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

input:focus {
  outline: none !important;
}

/* Animation Classes */
.animate-element {
  opacity: 0;
  filter: blur(8px);
  transform: translateY(30px);
  animation: fadeSlideIn 0.8s ease-out forwards;
}

@keyframes fadeSlideIn {
  to {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0px);
  }
}

.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

.animate-delay-600 {
  animation-delay: 0.6s;
}

.animate-delay-700 {
  animation-delay: 0.7s;
}

.animate-delay-800 {
  animation-delay: 0.8s;
}

.animate-delay-900 {
  animation-delay: 0.9s;
}

.animate-delay-1000 {
  animation-delay: 1s;
}

.animate-delay-1100 {
  animation-delay: 1.1s;
}

.animate-delay-1200 {
  animation-delay: 1.2s;
}

.animate-delay-1300 {
  animation-delay: 1.3s;
}

.animate-delay-1400 {
  animation-delay: 1.4s;
}

.animate-slide-right {
  opacity: 0;
  filter: blur(8px);
  transform: translateX(-50px);
  animation: slideRightIn 0.8s ease-out forwards;
}

@keyframes slideRightIn {
  to {
    opacity: 1;
    filter: blur(0px);
    transform: translateX(0px);
  }
}

.animate-testimonial {
  opacity: 0;
  filter: blur(6px);
  transform: translateY(40px) scale(0.95);
  animation: testimonialIn 0.8s ease-out forwards;
}

@keyframes testimonialIn {
  to {
    opacity: 1;
    filter: blur(0px);
    transform: translateY(0px) scale(1);
  }
}

@keyframes bg-pan {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-bg-pan {
  background-size: 200% 200%;
  animation: bg-pan 1.5s ease-in infinite;
}

.vibrate-1 {
  -webkit-animation: vibrate-1 0.5s linear infinite both;
  transform: scale(0.98);
  animation: vibrate-1 0.5s linear infinite both;
}
@-webkit-keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  20% {
    -webkit-transform: translate(-2px, 2px);
    transform: translate(-2px, 2px);
  }
  40% {
    -webkit-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px);
  }
  60% {
    -webkit-transform: translate(2px, 2px);
    transform: translate(2px, 2px);
  }
  80% {
    -webkit-transform: translate(2px, -2px);
    transform: translate(2px, -2px);
  }
  100% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
@keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  20% {
    -webkit-transform: translate(-2px, 2px);
    transform: translate(-2px, 2px);
  }
  40% {
    -webkit-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px);
  }
  60% {
    -webkit-transform: translate(2px, 2px);
    transform: translate(2px, 2px);
  }
  80% {
    -webkit-transform: translate(2px, -2px);
    transform: translate(2px, -2px);
  }
  100% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
