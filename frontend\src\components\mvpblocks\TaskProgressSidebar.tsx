"use client";

import { AnimatePresence, motion } from "framer-motion";
import { Accordion } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CheckCircle2, Clock, AlertCircle, Loader } from "lucide-react";
import { cn } from "@/lib/utils";

interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

interface TaskProgressSidebarProps {
  isGenerating: boolean;
  tasks: Task[];
  currentGeneratingScene?: string | null;
}

export default function TaskProgressSidebar({
  isGenerating = true,
  tasks = [
    { id: "1", name: "Download assets", status: "completed" },
    { id: "2", name: "Generate script", status: "completed" },
    { id: "3", name: "Render video", status: "in-progress" },
    { id: "4", name: "Upload to cloud", status: "pending" },
    { id: "5", name: "Notify user", status: "pending" },
  ],
  currentGeneratingScene = "Scene 2: Rendering animation",
}: TaskProgressSidebarProps) {
  const getStatusIcon = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "in-progress":
        return <Loader className="h-4 w-4 text-cyan-500 animate-spin" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return "bg-gradient-to-r via-green-500/10 border-green-500/20";
      case "in-progress":
        return "bg-gradient-to-r via-cyan-400/20 border-cyan-500/20";
      case "pending":
        return "bg-gradient-to-r via-yellow-500/10 border-yellow-500/20";
      case "failed":
        return "bg-gradient-to-r via-rose-500/10 border-rose-500/20";
      default:
        return "bg-secondary/30";
    }
  };

  if (!isGenerating) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="col-span-1 lg:col-span-2 h-full flex flex-col border-dashed bg-background"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <div className="px-6 py-4 border-b border-dashed bg-background">
          <h3 className="text-lg">Task Progress</h3>
        </div>

        <div className="flex-1 px-6 pt-4 pb-10">
          <div className="space-y-6">
            {/* Current Tasks */}
            <div className="w-full">
              <div className="flex items-center mb-4">
                <div className="w-1 h-6 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-3"></div>
                <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                  Current Progress
                </h4>
              </div>
              <div className="space-y-2">
                {tasks.map((task, index) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                    className={cn(
                      "relative overflow- w-full rounded-lg border cursor-pointer hover:saturate-150 hover:brightness-150 transition-all duration-300",
                      getStatusColor(task.status),
                      task.status === "in-progress" && "animate-bg-pan"
                    )}
                  >
                    <div className="flex items-center space-x-3 relative p-4 pr-2">
                      <div
                        className={`p-2 rounded-lg transition-all duration-200 ${
                          task.status === "in-progress"
                            ? "bg-primary/10 inset-shadow-xs inset-shadow-primary/30"
                            : task.status === "completed"
                            ? "bg-green-500/10 inset-shadow-xs inset-shadow-green-600/30"
                            : task.status === "failed"
                            ? "bg-rose-500/20"
                            : "bg-yellow-500/20"
                        }`}
                      >
                        {getStatusIcon(task.status)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-xs">{task.name}</h4>
                        {task.status === "in-progress" &&
                          currentGeneratingScene && (
                            <motion.p
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              className="text-xs text-muted-foreground mt-1 truncate"
                            >
                              {currentGeneratingScene}
                            </motion.p>
                          )}
                        {task.status === "in-progress" && (
                          <div className="mt-2 w-full bg-secondary rounded-full h-1.5 relative">
                            <div className="bg-gradient-to-r from-teal-300 via-blue-500 to-violet-400 h-1.5 rounded-full animate-pulse w-3/4"></div>
                            <div className="bg-gradient-to-r from-teal-300 via-blue-500 to-violet-400 h-1.5 rounded-full animate-pulse w-3/4 absolute top-0 left-0 blur animate-pulse translate-y-0.5"></div>
                          </div>
                        )}
                      </div>
                      <div
                        className={`text-[10px] font-medium px-2.5 py-1 rounded-full transition-all duration-200 ${
                          task.status === "completed"
                            ? "bg-green-500/10 text-green-500 border border-green-500/30"
                            : task.status === "in-progress"
                            ? "bg-cyan-500/10 text-cyan-500 border border-cyan-500/30"
                            : task.status === "failed"
                            ? "bg-rose-500/10 text-rose-500 border border-rose-500/30"
                            : "bg-yellow-500/10 text-yellow-500 border border-yellow-500/30"
                        }`}
                      >
                        {task.status}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Progress Summary */}
            <div className="p-4 bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-xl border border-border/30">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-muted-foreground">
                  Overall Progress
                </span>
                <span className="text-sm font-bold">
                  {tasks.filter((t) => t.status === "completed").length}/
                  {tasks.length}
                </span>
              </div>
              <div className="w-full rounded-full h-2 relative">
                <div
                  className="bg-gradient-to-r from-purple-500 to-violet-500 via-fuchsia-400  h-2 rounded-full transition-all duration-500"
                  style={{
                    width: `${
                      (tasks.filter((t) => t.status === "completed").length /
                        tasks.length) *
                      100
                    }%`,
                  }}
                ></div>
                <div
                  className={cn(
                    "bg-gradient-to-r from-purple-500 to-violet-500 via-fuchsia-400 h-2 rounded-full transition-all duration-500 absolute top-0 left-0 blur animate-pulse translate-y-0.5"
                  )}
                  style={{
                    width: `${
                      (tasks.filter((t) => t.status === "completed").length /
                        tasks.length) *
                      100
                    }%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
