import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict


class QueueConfig(BaseSettings):
    """Configuration for simple Python Queue-based job processing."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # Queue Settings
    max_retries: int = 3
    max_workers: int = 1  # Number of worker threads
    queue_timeout: int = 30  # Timeout for queue operations in seconds

    # Job Settings
    job_timeout_seconds: int = 300  # 5 minutes
    cleanup_completed_jobs_after_hours: int = 24

    # Cloudinary Configuration
    cloudinary_cloud_name: str = ""
    cloudinary_api_key: str = ""
    cloudinary_api_secret: str = ""
    cloudinary_upload_preset: str = ""  # For unsigned uploads (optional)

    # Backend API Configuration
    backend_api_base_url: str = (
        ""  # URL to the frontend API (e.g., http://localhost:3000)
    )


# Global configuration instance
_queue_config: Optional[QueueConfig] = None


def get_queue_config() -> QueueConfig:
    """Get the global queue configuration instance."""
    global _queue_config
    if _queue_config is None:
        _queue_config = QueueConfig()
    return _queue_config


def validate_config() -> bool:
    """Validate that all required configuration is present."""
    try:
        config = get_queue_config()
        # For simple queue system, we only need basic configuration
        # Most validation will be done when services are actually used
        return True
    except Exception:
        return False
