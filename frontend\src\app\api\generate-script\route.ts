import { generateObject } from "ai";
import { groq } from "@ai-sdk/groq";
import { z } from "zod";
import { NextResponse } from "next/server";

export const maxDuration = 30;

export async function POST(req: Request) {
  const { prompt }: { prompt: string } = await req.json();

  const { object } = await generateObject({
    model: groq("deepseek-r1-distill-llama-70b"),
    system: `You are an Educational Content Creator for Manim animations. Transform prompts into 2 scene scripts for mathematical animations. Explain topics clearly for students with no prior knowledge using simple language, analogies, and step-by-step breakdowns.
**Scene Framework:**
- **Scene 1: Title & Hook** - Animated title with visual hook
- **Scene 2: Core Concept** - Define using shapes, arrows, math representations
**Use Only These Manim Elements:**
- Shapes: Circles, Squares, Rectangles, Lines, Arrows, Polygons
- Text: Labels, MathTex (simple LaTeX only)
- Math: NumberLines, Axes, BarCharts, Tables, Coordinates
- Patterns: Step reveals, shape transformations, number sequences, flowcharts, before/after comparisons
**Avoid:** Photos, realistic images, external files, 3D models, complex illustrations
**For Each Scene:**
1. Scene Title
2. Manim Visual Elements
3. Animation Sequence
4. Key Message
5. Transition to next scene

Keep visuals simple, clean, and easily implementable in Manim.`,
    output: "array",
    schema: z.object({
      title: z.string().describe("The title of the video scene"),
      description: z
        .string()
        .describe(
          "The main description of the scene and all the details in depth"
        ),
    }),
    prompt,
    schemaName: "ManimScript",
    schemaDescription: "A detailed video script for Manim animation",
    maxRetries: 3,
  });

  return NextResponse.json(object);
}
// - **Scene 3: Step-by-Step** - Process via animated transformations
// - **Scene 4: Real-World** - Visual metaphors with geometric shapes
// - **Scene 5: Summary** - Recap with key formula/visual
