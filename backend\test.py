import requests
import json

# Test the new batch_render API endpoint
API_URL = "http://127.0.0.1:8000/batch_render"

# Sample payload matching the new format: { topicName: "", entryId: "", scripts: [{ manim_code: "", description: "" }] }
payload = {
    "topicName": "IDK",
    "entryId": "xxx",
    "scripts": [
        {
            "manim_code": """
from manim import *

class IntroScene(Scene):
    def construct(self):
        title = Text("Simple Harmonic Motion", font_size=48)
        self.play(Write(title))
        self.wait(2)
        self.play(FadeOut(title))

        subtitle = Text("Understanding Oscillatory Motion", font_size=32)
        self.play(Write(subtitle))
        self.wait(2)
        self.play(FadeOut(subtitle))
""",
            "description": "Introduction to simple harmonic motion, explaining the basic concept of oscillatory motion and its importance in physics.",
        },
        {
            "manim_code": """
from manim import *

class PendulumDemo(Scene):
    def construct(self):
        # Pendulum setup
        pendulum_rod = Line(start=UP * 2, end=ORIGIN, color=GRAY)
        pendulum_bob = Circle(radius=0.3, color=BLUE, fill_opacity=1)
        pendulum = Group(pendulum_rod, pendulum_bob)
        self.play(Create(pendulum_rod), Create(pendulum_bob))
        self.wait(1)

        # Pendulum swing
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(RIGHT * 4).rotate(-1),
            run_time=3
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(1)
        self.play(FadeOut(pendulum))
""",
            "description": "Demonstration of a pendulum showing how it swings back and forth in a regular pattern, illustrating the restoring force that brings it back to equilibrium.",
        },
        {
            "manim_code": """
from manim import *

class SpringDemo(Scene):
    def construct(self):
        # Spring setup
        spring_lines = []
        for i in range(10):
            line = Line(
                start=LEFT * 0.5 + UP * i * 0.2,
                end=LEFT * 0.5 + UP * (i + 1) * 0.2 + RIGHT * 0.2,
                color=YELLOW,
                stroke_width=4
            )
            spring_lines.append(line)

        spring = VGroup(*spring_lines)
        spring.move_to(UP * 2)
        mass = Square(side_length=0.6, color=RED, fill_opacity=1).shift(DOWN * 2)
        self.play(Create(spring), Create(mass))
        self.wait(1)

        # Spring oscillation
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(LEFT * 3),
            spring.animate.shift(LEFT * 3),
            run_time=2
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(1)
        self.play(FadeOut(spring), FadeOut(mass))
""",
            "description": "Spring-mass system demonstration showing how the mass oscillates when displaced from its equilibrium position, with the spring providing the restoring force.",
        },
    ],
    "priority": 0,
}

print("Testing batch_render API endpoint...")
print("API URL:", API_URL)
print("Payload:")
print(json.dumps(payload, indent=2))
print("\n" + "=" * 50 + "\n")

try:
    response = requests.post(API_URL, json=payload, timeout=30)

    print("Status Code:", response.status_code)
    print("Response Headers:", dict(response.headers))

    try:
        response_data = response.json()
        print("Response JSON:")
        print(json.dumps(response_data, indent=2))

        if response.status_code == 200:
            print(f"\n✅ Success! Job queued with ID: {response_data.get('job_id')}")
            if "queue_position" in response_data:
                print(f"📍 Queue Position: {response_data['queue_position']}")
            print(f"📝 Message: {response_data.get('message')}")
        else:
            print(f"\n❌ Error: {response_data.get('detail', 'Unknown error')}")

    except json.JSONDecodeError:
        print("Failed to parse JSON. Raw response:")
        print(response.text)

except requests.exceptions.RequestException as e:
    print(f"❌ Request failed: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")

print("\n" + "=" * 50)
print("Test completed!")
