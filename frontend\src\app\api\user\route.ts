// app/api/user/route.ts

import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getUser } from "@/actions/user.actions";

export async function GET() {
  const session = await auth();

  if (!session) {
    return NextResponse.json({ user: null }, { status: 401 });
  }

  const dbUser = await getUser(session.user!.id!);
  return NextResponse.json({ user: dbUser });
}
