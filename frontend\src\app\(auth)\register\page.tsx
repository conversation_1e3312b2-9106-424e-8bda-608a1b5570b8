"use client";

import { useState } from "react";
import { login, resendLogin } from "@/actions/auth.actions";

export default function SignIn() {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    if (email === "") {
      setError("Please enter your email address.");
      setLoading(false);
      return;
    }
    if (!/^\S+@\S+\.\S+$/.test(email)) {
      setError("Please enter a valid email address.");
      setLoading(false);
      return;
    }
    try {
      await resendLogin(email);
    } catch (error) {
      setError("An error occurred. Please try again later.");
      console.error(error);
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row overflow-x-hidden">
      {/* Left Column */}
      <section className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md">
          <div className="flex flex-col gap-6 p-8 md:p-0">
            {/* Heading */}
            <h1 className="animate-element animate-delay-100 text-4xl md:text-5xl font-semibold leading-tight">
              <span className="font-light tracking-tighter">Welcome</span>
            </h1>
            <p className="animate-element animate-delay-200 text-muted-foreground">
              Access your account and continue your journey with us
            </p>

            {/* Form */}
            <div className="space-y-5">
              {/* Email */}
              <div className="animate-element animate-delay-300">
                <label className="text-sm font-medium text-secondary-foreground">
                  Email Address
                </label>
                <div className="glass-border rounded-2xl mt-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value);
                      if (error) setError(""); // Clear error on input change
                    }}
                    placeholder="Enter your email address"
                    className="w-full bg-transparent text-sm pt-4 pr-4 pb-4 pl-4 rounded-2xl"
                  />
                </div>
              </div>

              {/* Options */}
              <div className="animate-element animate-delay-500 flex items-center justify-between text-sm">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input type="checkbox" className="custom-checkbox" />
                  <span className="text-muted-foreground">
                    Keep me signed in
                  </span>
                </label>
              </div>

              {/* Submit */}
              <button
                onClick={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
                disabled={loading}
                className="animate-element animate-delay-600 w-full rounded-2xl dark:bg-white py-4 font-medium dark:text-zinc-900 dark:hover:bg-zinc-100 transition-colors bg-black text-zinc-100 hover:bg-zinc-800/80 cursor-pointer"
              >
                Sign In
              </button>

              {/* Error Message */}
              {error && (
                <p className="animate-element text-red-500 text-sm">{error}</p>
              )}
            </div>

            {/* Divider */}
            <div className="animate-element animate-delay-700 relative flex items-center justify-center">
              <span className="w-24 border-t border-zinc-800"></span>
              <span className="px-4 text-sm text-zinc-500">
                Or continue with
              </span>
              <span className="w-24 border-t border-zinc-800"></span>
            </div>

            {/* Google Sign In */}
            <button
              className="animate-element animate-delay-800 w-full flex items-center justify-center gap-3 glass-border rounded-2xl py-4 cursor-pointer whitespace-nowrap font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95 duration-300 before:content[''] after:content[''] relative overflow-hidden before:absolute before:-left-6 before:top-7 before:z-10 before:h-12 before:w-12 before:rounded-full before:bg-red-500 before:blur-lg after:absolute after:-right-6 after:top-7 after:z-10 after:h-12 after:w-12 after:rounded-full after:bg-yellow-500 after:blur-lg hover:before:animate-pulse hover:after:animate-pulse"
              onClick={() => {
                login("google");
              }}
            >
              {/* Add your SVG icon here */}
              Continue with Google
            </button>

            <button
              className="animate-element animate-delay-800 w-full flex items-center justify-center gap-3 glass-border rounded-2xl py-4 cursor-pointer whitespace-nowrap font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95 duration-300 before:content[''] after:content[''] relative overflow-hidden before:absolute before:-left-6 before:top-7 before:z-10 before:h-12 before:w-12 before:rounded-full before:bg-purple-500 before:blur-lg after:absolute after:-right-6 after:top-7 after:z-10 after:h-12 after:w-12 after:rounded-full after:bg-violet-500 after:blur-lg hover:before:animate-pulse hover:after:animate-pulse"
              onClick={() => {
                login("github");
              }}
            >
              {/* Add your SVG icon here */}
              Continue with Github
            </button>

            <p className="animate-element animate-delay-900 text-center text-sm text-zinc-500">
              New to our platform?{" "}
              <a
                href="#"
                className="text-violet-400 hover:underline transition-colors"
              >
                Create Account
              </a>
            </p>
          </div>
        </div>
      </section>

      {/* Right Column */}
      <section className="hidden lg:block flex-[1.3] relative p-4">
        <img
          src="/bg.png"
          alt="Logo"
          className="animate-slide-right w-full h-full animate-delay-300 object-cover rounded-3xl shadow-lg backdrop-blur-lg border border-white/5 dark:border-zinc-800/40 shadow-black/20"
        />

        {/* Testimonials */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex gap-4 px-8">
          {[
            {
              name: "Sarah Chen",
              username: "@sarahdigital",
              text: "Amazing platform! The user experience is seamless and the features are exactly what I needed.",
              img: "https://randomuser.me/api/portraits/women/57.jpg",
              delay: "1000",
              show: true,
            },
            {
              name: "Marcus Johnson",
              username: "@marcustech",
              text: "This service has transformed how I work. Clean design, powerful features, and excellent support.",
              img: "https://randomuser.me/api/portraits/men/64.jpg",
              delay: "1200",
              show: true,
              className: "hidden xl:flex",
            },
            {
              name: "David Martinez",
              username: "@davidcreates",
              text: "I've tried many platforms, but this one stands out. Intuitive, reliable, and genuinely helpful for productivity.",
              img: "https://randomuser.me/api/portraits/men/32.jpg",
              delay: "1400",
              show: true,
              className: "hidden 2xl:flex",
            },
          ].map((card, i) =>
            card.show ? (
              <div
                key={i}
                className={`animate-testimonial animate-delay-${card.delay} ${
                  card.className || ""
                } flex items-start gap-3 rounded-3xl bg-zinc-800/5 dark:bg-zinc-800/40 backdrop-blur-xl border border-white/10 p-5 w-64`}
              >
                <img
                  src={card.img}
                  className="h-10 w-10 object-cover rounded-2xl"
                  alt="avatar"
                />
                <div className="text-sm leading-snug">
                  <p className="flex text-white items-center gap-1 font-medium">
                    {card.name}
                  </p>
                  <p className="text-zinc-500/60">{card.username}</p>
                  <p className="mt-1 text-zinc-300/70">{card.text}</p>
                </div>
              </div>
            ) : null
          )}
        </div>
      </section>
    </div>
  );
}
