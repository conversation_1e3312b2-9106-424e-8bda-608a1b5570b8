import os
import subprocess
import tempfile
import uuid
import logging
from typing import List, Optional
from dotenv import load_dotenv
from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)


class VideoMergingService:
    """Service for merging multiple video files into a single video."""

    def __init__(self):
        self.config = get_queue_config()
        self.result_dir = "media/result"
        os.makedirs(self.result_dir, exist_ok=True)

    def merge_multiple_videos(self, video_paths: List[str], topic_name: str) -> Optional[str]:
        """
        Merge multiple video files into a single video - following x.py implementation.

        Args:
            video_paths: List of paths to video files to merge
            topic_name: Name of the topic for output filename

        Returns:
            Path to merged video file or None if failed
        """
        if not video_paths:
            logger.error("❌ No videos to merge")
            return None

        # Create output path
        final_output_path = os.path.join(
            self.result_dir,
            f"{topic_name.lower()}_final_merged.mp4"
        )

        list_file = os.path.join(tempfile.gettempdir(), f"{uuid.uuid4().hex}_videos.txt")

        with open(list_file, "w") as f:
            for path in video_paths:
                f.write(f"file '{os.path.abspath(path)}'\n")

        try:
            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-f",
                    "concat",
                    "-safe",
                    "0",
                    "-i",
                    list_file,
                    "-c",
                    "copy",
                    final_output_path,
                ],
                check=True,
            )
            logger.info(f"✅ All videos merged into: {final_output_path}")
            return final_output_path
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to merge videos: {e}")
            return None
        finally:
            # Clean up temporary list file
            if os.path.exists(list_file):
                try:
                    os.remove(list_file)
                except:
                    pass

    def merge_videos_with_transitions(self, video_paths: List[str], topic_name: str) -> Optional[str]:
        """
        Merge videos with smooth transitions between them.
        
        Args:
            video_paths: List of paths to video files to merge
            topic_name: Name of the topic for output filename
            
        Returns:
            Path to merged video file with transitions or None if failed
        """
        if not video_paths:
            logger.error("No videos to merge")
            return None

        if len(video_paths) == 1:
            # If only one video, just copy it to the result directory
            return self._copy_single_video(video_paths[0], topic_name)

        try:
            # Create output path
            final_output_path = os.path.join(
                self.result_dir, 
                f"{topic_name.lower()}_final_merged.mp4"
            )

            # Build complex ffmpeg filter for crossfade transitions
            filter_complex = self._build_crossfade_filter(video_paths)

            # Run ffmpeg with complex filter
            cmd = [
                "ffmpeg", "-y"
            ]

            # Add input files
            for path in video_paths:
                cmd.extend(["-i", path])

            # Add filter complex and output
            cmd.extend([
                "-filter_complex", filter_complex,
                "-map", "[final]",
                "-c:v", "libx264",
                "-c:a", "aac",
                final_output_path,
            ])

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
            )

            logger.info(f"Videos merged with transitions: {final_output_path}")
            return final_output_path

        except subprocess.CalledProcessError as e:
            logger.error(f"Video merge with transitions failed: {e.stderr}")
            # Fallback to simple concatenation
            logger.info("Falling back to simple concatenation")
            return self.merge_multiple_videos(video_paths, topic_name)
        except Exception as e:
            logger.error(f"Video merge error: {e}")
            return None

    def _copy_single_video(self, video_path: str, topic_name: str) -> Optional[str]:
        """Copy a single video to the result directory."""
        try:
            final_output_path = os.path.join(
                self.result_dir, 
                f"{topic_name.lower()}_final_merged.mp4"
            )

            cmd = [
                "ffmpeg", "-y",
                "-i", video_path,
                "-c", "copy",
                final_output_path,
            ]

            subprocess.run(cmd, check=True, capture_output=True)
            logger.info(f"Single video copied: {final_output_path}")
            return final_output_path

        except subprocess.CalledProcessError as e:
            logger.error(f"Single video copy failed: {e}")
            return None

    def _build_crossfade_filter(self, video_paths: List[str]) -> str:
        """Build ffmpeg filter complex for crossfade transitions."""
        if len(video_paths) < 2:
            return "[0:v][0:a]"

        # Simple concatenation filter for now
        # In the future, this could be enhanced with actual crossfade effects
        video_inputs = "".join(f"[{i}:v][{i}:a]" for i in range(len(video_paths)))
        return f"{video_inputs}concat=n={len(video_paths)}:v=1:a=1[final]"

    def cleanup_merged_videos(self, *video_paths):
        """Clean up intermediate video files."""
        for path in video_paths:
            if path and os.path.exists(path):
                try:
                    os.remove(path)
                    logger.debug(f"Cleaned up video file: {path}")
                except Exception as e:
                    logger.warning(f"Failed to remove video file {path}: {e}")

    def get_video_info(self, video_path: str) -> dict:
        """Get video information using ffprobe."""
        try:
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path,
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
            )

            import json
            return json.loads(result.stdout)

        except Exception as e:
            logger.error(f"Failed to get video info for {video_path}: {e}")
            return {}


# Global service instance
_video_merging_service: Optional[VideoMergingService] = None


def get_video_merging_service() -> VideoMergingService:
    """Get the global VideoMergingService instance."""
    global _video_merging_service
    if _video_merging_service is None:
        _video_merging_service = VideoMergingService()
    return _video_merging_service
