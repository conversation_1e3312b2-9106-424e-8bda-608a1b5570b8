import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const { id, videoUrl } = await request.json();

    if (!id || !videoUrl) {
      return NextResponse.json(
        { error: "Missing required parameters: id and videoUrl" },
        { status: 400 }
      );
    }

    const updatedEntry = await db.entries.update({
      where: { id },
      data: { videoUrl },
    });

    if (!updatedEntry) {
      return NextResponse.json({ error: "Entry not found" }, { status: 404 });
    }

    return NextResponse.json(
      {
        success: true,
        message: "Video URL updated successfully",
        entry: updatedEntry,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating video URL:", error);
    return NextResponse.json(
      { error: "Failed to update video URL" },
      { status: 500 }
    );
  }
}
