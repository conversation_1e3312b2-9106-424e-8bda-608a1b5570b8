import os
import subprocess
import tempfile
import requests
import sys
import uuid
import re
import shutil
from dotenv import load_dotenv
import textwrap  # <-- Add this import

dotenv_path = os.path.join(os.path.dirname(__file__), ".env")
load_dotenv(dotenv_path=dotenv_path)

GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
TTS_ENDPOINT = os.getenv("TTS_ENDPOINT")
CLOUDINARY_CLOUD_NAME = os.getenv("CLOUDINARY_CLOUD_NAME")
CLOUDINARY_API_KEY = os.getenv("CLOUDINARY_API_KEY")
CLOUDINARY_API_SECRET = os.getenv("CLOUDINARY_API_SECRET")
CLOUDINARY_FOLDER = "clarifai"
CLOUDINARY_UPLOAD_PRESET = os.getenv("CLOUDINARY_UPLOAD_PRESET")
BACKEND_API_BASE_URL = os.getenv("BACKEND_API_BASE_URL")

AUDIO_DIR = "media/audio"
VIDEO_DIR = "media/videos"
RESULT_DIR = "media/result"

try:
    import google.generativeai as genai

    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash")
except Exception as e:
    print("Gemini init failed:", e)
    sys.exit(1)


def generate_narration(manim_code: str, duration: int = 30) -> str:
    system_prompt = """
You are a professional educational narrator and a subject matter expert in every concepts and animation. You speak with calm authority, disciplined clarity, and unwavering focus — as if addressing a lecture hall of advanced students. You are not here to entertain; you are here to educate. You do not describe or refer to the code itself. Instead, mentally visualize the animation it produces using Manim and generate an accurate, concise voiceover script.

Your narration must:
- Describe what the viewer is seeing visually, step by step
- Explain the conceptual and theoretical ideas being illustrated in real time
- Use academic, confident, and succinct language
- Remain strictly within the requested duration
- Be under 250 words

Focus on clarity, precision, and synchronization with the animation timeline. Never go off-topic, add filler, or explain programming details. Your goal is to illuminate the animation's visuals and their underlying concept — with discipline and authority.
"""

    user_prompt = f"""
Generate a concise, authoritative narration script for the following Manim animation.

⚠️ The narration must:
- Be exactly {duration} seconds long
- Be synced to an animation with a tempo of 1.0 (do not expect speed adjustments)
- Stay under 250 words
- Explain both the concepts and the visual flow step-by-step
- Use a formal, educational tone

You are not allowed to exceed the given duration. Do not refer to the code or how it works. Focus entirely on what is shown visually and what it means mathematically.

Here is the Manim code:

{manim_code}
"""

    messages_chatgpt = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]

    def transform_to_gemini(messages_chatgpt):
        messages_gemini = []
        system_prompt = ""
        for message in messages_chatgpt:
            if message["role"] == "system":
                system_prompt = message["content"]
            elif message["role"] == "user":
                messages_gemini.append({"role": "user", "parts": [message["content"]]})
            elif message["role"] == "assistant":
                messages_gemini.append({"role": "model", "parts": [message["content"]]})
        if system_prompt:
            messages_gemini[0]["parts"].insert(0, f"*{system_prompt}*")
        return messages_gemini

    try:
        messages = transform_to_gemini(messages_chatgpt)
        response = model.generate_content(messages)
        return response.text.strip()
    except Exception as e:
        print("Narration generation failed:", e)
        return ""


def generate_tts_audio(text: str):
    headers = {"Content-Type": "application/json"}
    os.makedirs(AUDIO_DIR, exist_ok=True)
    temp_audio = tempfile.NamedTemporaryFile(suffix=".mp3", dir=AUDIO_DIR, delete=False)
    try:
        response = requests.post(TTS_ENDPOINT, headers=headers, json={"text": text})
        response.raise_for_status()
        temp_audio.write(response.content)
        temp_audio.flush()
        print("🔊 TTS audio ready")
        return temp_audio.name
    except Exception as e:
        print("TTS generation failed:", e)
        return None


def render_manim_video(code_str: str, class_name: str, video_id: str):
    py_file = f"{video_id}.py"
    # Remove leading/trailing whitespace and dedent for clean code
    code_str = textwrap.dedent(code_str.strip())
    with open(py_file, "w", encoding="utf-8") as f:
        f.write(code_str)

    try:
        subprocess.run(
            [
                "manim",
                "-qk",
                "-o",
                f"{video_id}.mp4",
                py_file,
                class_name,
                "--media_dir",
                "media",
            ],
            capture_output=True,
            text=True,
            check=True,
        )
    except subprocess.CalledProcessError as e:
        print("🚫 Manim render failed!", e.stdout, e.stderr)
        if os.path.exists(py_file):
            os.remove(py_file)
        return None, None

    for root, _, files in os.walk(VIDEO_DIR):
        for file in files:
            if file == f"{video_id}.mp4":
                return os.path.join(root, file), py_file
    raise FileNotFoundError("❌ Video not found in media/videos")


def get_media_duration(path: str) -> float:
    try:
        result = subprocess.run(
            [
                "ffprobe",
                "-v",
                "error",
                "-show_entries",
                "format=duration",
                "-of",
                "default=noprint_wrappers=1:nokey=1",
                path,
            ],
            capture_output=True,
            text=True,
            check=True,
        )
        return float(result.stdout.strip())
    except Exception as e:
        print(f"⚠️ Could not get duration for {path}:", e)
        return 0.0


def merge_audio_video(video_path: str, audio_path: str, video_id: str):
    os.makedirs(RESULT_DIR, exist_ok=True)
    stretched_audio = os.path.join(AUDIO_DIR, f"{video_id}_adjusted.aac")
    final_output = os.path.join(RESULT_DIR, f"{video_id}_final.mp4")

    video_duration = get_media_duration(video_path)
    audio_duration = get_media_duration(audio_path)

    if audio_duration == 0 or video_duration == 0:
        print("❌ Invalid durations")
        return None

    if audio_duration > video_duration:
        tempo_ratio = int(audio_duration / video_duration)
    else:
        tempo_ratio = int(video_duration / audio_duration)

    print(f"🎚️ Adjusting audio tempo: {tempo_ratio}x")

    try:
        subprocess.run(
            [
                "ffmpeg",
                "-y",
                "-i",
                audio_path,
                "-filter:a",
                f"atempo={tempo_ratio}",
                "-vn",
                "-acodec",
                "aac",
                stretched_audio,
            ],
            check=True,
        )

        subprocess.run(
            [
                "ffmpeg",
                "-y",
                "-i",
                video_path,
                "-i",
                stretched_audio,
                "-map",
                "0:v:0",
                "-map",
                "1:a:0",
                "-c:v",
                "copy",
                "-c:a",
                "aac",
                "-shortest",
                final_output,
            ],
            check=True,
        )

        print(f"✅ Final video saved at: {final_output}")

        os.remove(audio_path)
        os.remove(stretched_audio)

        return final_output

    except subprocess.CalledProcessError as e:
        print("❌ FFmpeg merge failed:", e)
        return None


def upload_to_cloudinary(video_path, video_id, scene_name):
    try:
        import cloudinary
        import cloudinary.uploader

        cloudinary.config(
            cloud_name=CLOUDINARY_CLOUD_NAME,
            api_key=CLOUDINARY_API_KEY,
            api_secret=CLOUDINARY_API_SECRET,
            secure=True,
        )
        return cloudinary.uploader.upload(
            video_path,
            resource_type="video",
            public_id=f"renders/{video_id}/{scene_name}",
            folder=CLOUDINARY_FOLDER,
            overwrite=True,
            upload_preset=CLOUDINARY_UPLOAD_PRESET,
        ).get("secure_url")
    except Exception as e:
        print("Cloudinary upload failed:", e)
        return None


def update_video_url_in_db(entry_id, video_url):
    try:
        resp = requests.post(
            f"{BACKEND_API_BASE_URL}/api/update-video",
            json={"id": entry_id, "videoUrl": video_url},
            headers={"Content-Type": "application/json"},
            timeout=30,
        )
        resp.raise_for_status()
        print(f"✅ Updated DB entry {entry_id}")
    except Exception as e:
        print(f"DB update failed for {entry_id}:", e)


def manim_topic_to_final_video(
    manim_codes: list, topic: str = "LinkedList", entry_id: str = None
):
    merged_clips = []

    for idx, code_str in enumerate(manim_codes):
        match = re.search(r"class\s+(\w+)\s*\(.*Scene.*\):", code_str)
        class_name = match.group(1) if match else f"AutoScene{idx}"
        video_id = str(uuid.uuid4())[:8]
        print(f"\n🔁 Processing snippet {idx + 1}/{len(manim_codes)} — ID: {video_id}")

        # STEP 1: Render Manim
        video_path, py_file = render_manim_video(code_str, class_name, video_id)
        if not video_path:
            continue

        # STEP 2: Duration
        video_duration = get_media_duration(video_path)
        if video_duration == 0:
            continue

        # STEP 3: Narration
        narration = generate_narration(code_str, duration=int(video_duration))
        if not narration:
            continue

        # STEP 4: TTS Audio
        audio_path = generate_tts_audio(narration)
        if not audio_path:
            continue

        # STEP 5: Merge video + audio
        final_path = merge_audio_video(video_path, audio_path, video_id)
        if final_path:
            merged_clips.append(final_path)

        # Cleanup
        if os.path.exists(py_file):
            os.remove(py_file)
        video_folder = os.path.join("media", "videos", video_id)
        if os.path.exists(video_folder):
            shutil.rmtree(video_folder)

    # STEP 6: Merge all final videos
    if not merged_clips:
        print("❌ No final clips to merge.")
        return

    final_merged_path = f"media/result/{topic.lower()}_final_merged.mp4"
    final_output = merge_multiple_videos(
        merged_clips, final_output_path=final_merged_path
    )
    if not final_output:
        return

    # STEP 7: Upload to Cloudinary
    cloud_url = upload_to_cloudinary(final_output, topic.lower(), f"{topic}Merged")
    if cloud_url:
        print(f"☁️ Uploaded Final Video: {cloud_url}")
        if entry_id:
            update_video_url_in_db(entry_id, cloud_url)


def merge_multiple_videos(video_paths, final_output_path):
    if not video_paths:
        print("❌ No videos to merge")
        return None

    list_file = os.path.join(tempfile.gettempdir(), f"{uuid.uuid4().hex}_videos.txt")

    with open(list_file, "w") as f:
        for path in video_paths:
            f.write(f"file '{os.path.abspath(path)}'\n")

    try:
        subprocess.run(
            [
                "ffmpeg",
                "-y",
                "-f",
                "concat",
                "-safe",
                "0",
                "-i",
                list_file,
                "-c",
                "copy",
                final_output_path,
            ],
            check=True,
        )
        print(f"✅ All videos merged into: {final_output_path}")
        return final_output_path
    except subprocess.CalledProcessError as e:
        print("❌ Failed to merge videos:", e)
        return None


if __name__ == "__main__":

    manim_codes = [
    """
    from manim import *

class SimpleHarmonicMotion(Scene):
    def construct(self):
        title = Text("Simple Harmonic Motion", font_size=48)
        self.play(Write(title))
        self.wait(2)
        self.play(FadeOut(title))

        # Pendulum setup
        pendulum_rod = Line(start=UP * 2, end=ORIGIN, color=GRAY)
        pendulum_bob = Circle(radius=0.3, color=BLUE, fill_opacity=1)
        pendulum = Group(pendulum_rod, pendulum_bob)
        self.play(Create(pendulum_rod), Create(pendulum_bob))
        self.wait(1)

        # Pendulum swing
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(RIGHT * 4).rotate(-1),
            run_time=3
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(1)
        self.play(FadeOut(pendulum))

        # Spring setup
        spring = VGroup(*[Line(start=LEFT * 0.5 + UP * i * 0.2, end=LEFT * 0.5 + UP * (i + 1) * 0.2 + RIGHT * 0.2, color=YELLOW, stroke_width=4) for i in range(10)])
        spring.move_to(UP * 2)
        mass = Square(side_length=0.6, color=RED, fill_opacity=1).shift(DOWN * 2)
        self.play(Create(spring), Create(mass))
        self.wait(1)

        # Spring oscillation
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(LEFT * 3),
            spring.animate.shift(LEFT * 3),
            run_time=2
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(1)
        self.play(FadeOut(spring), FadeOut(mass))

        # Summary
        summary_text = Text("Motion that repeats", font_size=36)
        self.play(Write(summary_text))
        self.wait(2)
        self.play(FadeOut(summary_text))
    """,
    
    """
    from manim import *

class SimpleHarmonicMotion(Scene):
    def construct(self):
        title = Text("Simple Harmonic Motion", font_size=48)
        self.play(Write(title))
        self.wait(2)
        self.play(FadeOut(title))

        # Pendulum setup
        pendulum_rod = Line(start=UP * 2, end=ORIGIN, color=GRAY)
        pendulum_bob = Circle(radius=0.3, color=BLUE, fill_opacity=1)
        pendulum = Group(pendulum_rod, pendulum_bob)
        self.play(Create(pendulum_rod), Create(pendulum_bob))
        self.wait(1)

        # Pendulum swing
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(RIGHT * 4).rotate(-1),
            run_time=3
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(1)
        self.play(FadeOut(pendulum))

        # Spring setup
        spring = VGroup(*[Line(start=LEFT * 0.5 + UP * i * 0.2, end=LEFT * 0.5 + UP * (i + 1) * 0.2 + RIGHT * 0.2, color=YELLOW, stroke_width=4) for i in range(10)])
        spring.move_to(UP * 2)
        mass = Square(side_length=0.6, color=RED, fill_opacity=1).shift(DOWN * 2)
        self.play(Create(spring), Create(mass))
        self.wait(1)

        # Spring oscillation
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(LEFT * 3),
            spring.animate.shift(LEFT * 3),
            run_time=2
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(1)
        self.play(FadeOut(spring), FadeOut(mass))

        # Summary
        summary_text = Text("Motion that repeats", font_size=36)
        self.play(Write(summary_text))
        self.wait(2)
        self.play(FadeOut(summary_text))
    """
    ]

    entry_id = "6859bfb05b27a548d571a05e"
    manim_topic_to_final_video(manim_codes, topic="LinkedList", entry_id=entry_id)
