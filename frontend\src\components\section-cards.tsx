import { IconTrendingUp, IconPlus, IconClock } from "@tabler/icons-react"

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

export function SectionCards() {
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      
      {/* Card 1: Videos Generated */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Videos Generated</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            5
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className="flex items-center gap-2">
              <IconTrendingUp className="size-6"/>
              +20.1%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            From yesterday <IconTrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Improved video creation rate
          </div>
        </CardFooter>
      </Card>

      {/* Card 2: Total Videos */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Total Videos</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            10
          </CardTitle>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            In your library <IconPlus className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Saved and ready to view anytime
          </div>
        </CardFooter>
      </Card>

      {/* Card 3: Quizzes Created */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Quizzes Created</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            3
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className="flex items-center gap-2">
              <IconPlus className="size-6"/>
              +2 today
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Active content creation <IconPlus className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Great progress this session
          </div>
        </CardFooter>
      </Card>

      {/* Card 4: Generation Time */}
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Generation Time</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
            1m
          </CardTitle>
          <CardAction>
            <Badge variant="outline" className="flex items-center gap-2">
              <IconClock className="size-6" />
              avg/video
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Efficient performance <IconClock className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Based on last 10 generations
          </div>
        </CardFooter>
      </Card>

    </div>
  )
}
