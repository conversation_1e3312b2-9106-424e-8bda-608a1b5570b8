import os
import tempfile
import subprocess
import logging
import requests
from typing import Optional
from dotenv import load_dotenv
from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)


class AudioService:
    """Service for text-to-speech generation and audio processing."""

    def __init__(self):
        self.config = get_queue_config()
        self.audio_dir = "media/audio"
        os.makedirs(self.audio_dir, exist_ok=True)

    def generate_tts_audio(self, text: str) -> Optional[str]:
        """
        Generate TTS audio from text - following x.py implementation.

        Args:
            text: Text to convert to speech

        Returns:
            Path to generated audio file or None if failed
        """
        try:
            tts_endpoint = os.getenv("TTS_ENDPOINT")
            if not tts_endpoint:
                logger.error("TTS_ENDPOINT environment variable not set")
                return None

            headers = {"Content-Type": "application/json"}
            temp_audio = tempfile.NamedTemporaryFile(
                suffix=".mp3",
                dir=self.audio_dir,
                delete=False
            )

            response = requests.post(
                tts_endpoint,
                headers=headers,
                json={"text": text}
            )
            response.raise_for_status()

            temp_audio.write(response.content)
            temp_audio.flush()
            temp_audio.close()

            logger.info("🔊 TTS audio ready")
            return temp_audio.name

        except Exception as e:
            logger.error(f"TTS generation failed: {e}")
            return None

    def adjust_audio_tempo(self, audio_path: str, target_duration: float, video_id: str) -> Optional[str]:
        """
        Adjust audio tempo to match target duration.
        
        Args:
            audio_path: Path to input audio file
            target_duration: Target duration in seconds
            video_id: Unique identifier for output file
            
        Returns:
            Path to adjusted audio file or None if failed
        """
        try:
            # Get current audio duration
            audio_duration = self._get_audio_duration(audio_path)
            if audio_duration <= 0:
                logger.error("Invalid audio duration")
                return None

            # Calculate tempo ratio
            if audio_duration > target_duration:
                tempo_ratio = audio_duration / target_duration
            else:
                tempo_ratio = target_duration / audio_duration

            # Clamp tempo ratio to reasonable bounds
            tempo_ratio = max(0.5, min(2.0, tempo_ratio))

            logger.info(f"Adjusting audio tempo by {tempo_ratio}x")

            # Create output path
            adjusted_audio = os.path.join(self.audio_dir, f"{video_id}_adjusted.aac")

            # Run ffmpeg to adjust tempo
            cmd = [
                "ffmpeg", "-y",
                "-i", audio_path,
                "-filter:a", f"atempo={tempo_ratio}",
                "-vn",
                "-acodec", "aac",
                adjusted_audio,
            ]

            subprocess.run(cmd, check=True, capture_output=True)
            logger.info(f"Audio tempo adjusted: {adjusted_audio}")
            
            return adjusted_audio

        except subprocess.CalledProcessError as e:
            logger.error(f"Audio tempo adjustment failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
            return None

    def merge_audio_video(self, video_path: str, audio_path: str, video_id: str) -> Optional[str]:
        """
        Merge audio and video files - following x.py implementation.

        Args:
            video_path: Path to video file
            audio_path: Path to audio file
            video_id: Unique identifier for output

        Returns:
            Path to merged video file or None if failed
        """
        result_dir = "media/result"
        os.makedirs(result_dir, exist_ok=True)
        stretched_audio = os.path.join(self.audio_dir, f"{video_id}_adjusted.aac")
        final_output = os.path.join(result_dir, f"{video_id}_final.mp4")

        video_duration = self._get_media_duration(video_path)
        audio_duration = self._get_media_duration(audio_path)

        if audio_duration == 0 or video_duration == 0:
            logger.error("❌ Invalid durations")
            return None

        if audio_duration > video_duration:
            tempo_ratio = int(audio_duration / video_duration)
        else:
            tempo_ratio = int(video_duration / audio_duration)

        logger.info(f"🎚️ Adjusting audio tempo: {tempo_ratio}x")

        try:
            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-i",
                    audio_path,
                    "-filter:a",
                    f"atempo={tempo_ratio}",
                    "-vn",
                    "-acodec",
                    "aac",
                    stretched_audio,
                ],
                check=True,
            )

            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-i",
                    video_path,
                    "-i",
                    stretched_audio,
                    "-map",
                    "0:v:0",
                    "-map",
                    "1:a:0",
                    "-c:v",
                    "copy",
                    "-c:a",
                    "aac",
                    "-shortest",
                    final_output,
                ],
                check=True,
            )

            logger.info(f"✅ Final video saved at: {final_output}")

            os.remove(audio_path)
            os.remove(stretched_audio)

            return final_output

        except subprocess.CalledProcessError as e:
            logger.error(f"❌ FFmpeg merge failed: {e}")
            return None

    def _get_audio_duration(self, audio_path: str) -> float:
        """Get audio file duration using ffprobe."""
        return self._get_media_duration(audio_path)

    def _get_video_duration(self, video_path: str) -> float:
        """Get video file duration using ffprobe."""
        return self._get_media_duration(video_path)

    def _get_media_duration(self, path: str) -> float:
        """Get media file duration using ffprobe."""
        try:
            result = subprocess.run(
                [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    path,
                ],
                capture_output=True,
                text=True,
                check=True,
            )
            return float(result.stdout.strip())
        except Exception as e:
            logger.warning(f"Could not get duration for {path}: {e}")
            return 0.0

    def cleanup_audio_files(self, *audio_paths):
        """Clean up audio files."""
        for path in audio_paths:
            if path and os.path.exists(path):
                try:
                    os.remove(path)
                except Exception as e:
                    logger.warning(f"Failed to remove audio file {path}: {e}")


# Global service instance
_audio_service: Optional[AudioService] = None


def get_audio_service() -> AudioService:
    """Get the global AudioService instance."""
    global _audio_service
    if _audio_service is None:
        _audio_service = AudioService()
    return _audio_service
