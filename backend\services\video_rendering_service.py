import os
import subprocess
import tempfile
import uuid
import re
import shutil
import logging
import textwrap
import requests
import json
from typing import Optional, List, Tuple
from dotenv import load_dotenv
from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)


class VideoRenderingService:
    """Service for rendering Manim videos and extracting scene information."""

    def __init__(self):
        self.config = get_queue_config()
        self.video_dir = "media/videos"
        os.makedirs(self.video_dir, exist_ok=True)

    def render_manim_video(self, code_str: str, video_id: str, max_retries: int = 3) -> Tuple[Optional[str], Optional[str]]:
        """
        Render a Manim video from code string with error handling and bugfix integration.

        Args:
            code_str: The Manim code to render
            video_id: Unique identifier for this video
            max_retries: Maximum number of retry attempts with bugfix

        Returns:
            Tuple of (video_path, py_file_path) or (None, None) if failed
        """
        current_code = code_str

        for attempt in range(max_retries + 1):
            logger.info(f"🎬 Rendering attempt {attempt + 1}/{max_retries + 1} for video {video_id}")

            # Extract class name from code
            class_name = self._extract_scene_name(current_code)

            # Create temporary Python file
            py_file = f"{video_id}.py"
            current_code = textwrap.dedent(current_code.strip())

            with open(py_file, "w", encoding="utf-8") as f:
                f.write(current_code)

            try:
                result = subprocess.run(
                    [
                        "manim",
                        "-qk",
                        "-o",
                        f"{video_id}.mp4",
                        py_file,
                        class_name,
                        "--media_dir",
                        "media",
                    ],
                    capture_output=True,
                    text=True,
                    check=True,
                )

                logger.info(f"✅ Manim render successful for {video_id}")

                # Find the rendered video - following x.py pattern
                for root, _, files in os.walk(self.video_dir):
                    for file in files:
                        if file == f"{video_id}.mp4":
                            video_path = os.path.join(root, file)
                            logger.info(f"📹 Video found at: {video_path}")
                            return video_path, py_file

                logger.error("❌ Video not found in media/videos")
                if os.path.exists(py_file):
                    os.remove(py_file)
                return None, None

            except subprocess.CalledProcessError as e:
                error_output = f"{e.stdout}\n{e.stderr}".strip()
                logger.error(f"🚫 Manim render failed (attempt {attempt + 1}): {error_output}")

                # Clean up partial files
                self._cleanup_partial_files(video_id, py_file)

                # If this is not the last attempt, try to fix the code
                if attempt < max_retries:
                    logger.info(f"🔧 Attempting to fix code using bugfix API...")
                    fixed_code = self._call_bugfix_api(current_code, error_output)

                    if fixed_code and fixed_code != current_code:
                        logger.info(f"🛠️ Code fixed, retrying render...")
                        current_code = fixed_code
                        continue
                    else:
                        logger.warning(f"⚠️ Bugfix API did not provide a fix, retrying with same code...")
                else:
                    logger.error(f"❌ Max retries ({max_retries}) exceeded for video {video_id}")

        return None, None

    def _extract_scene_name(self, code_str: str) -> str:
        """Extract the scene class name from Manim code."""
        match = re.search(r"class\s+(\w+)\s*\(.*Scene.*\):", code_str)
        if match:
            return match.group(1)
        
        # Fallback: look for any class that inherits from Scene
        lines = code_str.splitlines()
        for line in lines:
            if line.strip().startswith("class ") and "Scene" in line:
                class_name = line.split("(")[0].replace("class ", "").strip()
                if class_name:
                    return class_name
        
        return "AutoScene"

    def _find_rendered_video(self, video_id: str) -> Optional[str]:
        """Find the rendered video file in the media directory."""
        for root, _, files in os.walk(self.video_dir):
            for file in files:
                if file == f"{video_id}.mp4":
                    return os.path.join(root, file)
        return None

    def get_media_duration(self, path: str) -> float:
        """Get the duration of a media file using ffprobe."""
        try:
            result = subprocess.run(
                [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    path,
                ],
                capture_output=True,
                text=True,
                check=True,
            )
            return float(result.stdout.strip())
        except Exception as e:
            logger.warning(f"Could not get duration for {path}: {e}")
            return 0.0

    def _call_bugfix_api(self, code: str, error: str) -> Optional[str]:
        """Call the frontend bugfix API to fix manim code errors."""
        try:
            # Get the frontend API base URL from config
            frontend_url = os.getenv("FRONTEND_API_BASE_URL", "http://localhost:3000")
            bugfix_url = f"{frontend_url}/api/bugfix"

            payload = {
                "code": code,
                "error": error
            }

            logger.info(f"🔧 Calling bugfix API at {bugfix_url}")

            response = requests.post(
                bugfix_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            if response.status_code == 200:
                fixed_code = response.text.strip()
                logger.info(f"✅ Bugfix API returned fixed code")
                return fixed_code
            else:
                logger.error(f"❌ Bugfix API failed with status {response.status_code}: {response.text}")
                return None

        except Exception as e:
            logger.error(f"❌ Error calling bugfix API: {e}")
            return None

    def _cleanup_partial_files(self, video_id: str, py_file: str = None):
        """Clean up partial files after failed render."""
        try:
            logger.info(f"🧹 Cleaning up partial files for {video_id}")

            # Remove Python file
            if py_file and os.path.exists(py_file):
                os.remove(py_file)
                logger.debug(f"Removed Python file: {py_file}")

            # Remove any partial video files in media/videos
            for root, dirs, files in os.walk(self.video_dir):
                for file in files:
                    if video_id in file:
                        file_path = os.path.join(root, file)
                        os.remove(file_path)
                        logger.debug(f"Removed partial video file: {file_path}")

            # Remove video folder if it exists
            video_folder = os.path.join("media", "videos", video_id)
            if os.path.exists(video_folder):
                shutil.rmtree(video_folder)
                logger.debug(f"Removed video folder: {video_folder}")

        except Exception as e:
            logger.warning(f"Failed to cleanup partial files for {video_id}: {e}")

    def cleanup_temp_files(self, py_file: str, video_id: str):
        """Clean up temporary files created during rendering."""
        try:
            # Remove Python file
            if py_file and os.path.exists(py_file):
                os.remove(py_file)

            # Remove video folder
            video_folder = os.path.join("media", "videos", video_id)
            if os.path.exists(video_folder):
                shutil.rmtree(video_folder)

        except Exception as e:
            logger.warning(f"Failed to cleanup temp files for {video_id}: {e}")


# Global service instance
_video_rendering_service: Optional[VideoRenderingService] = None


def get_video_rendering_service() -> VideoRenderingService:
    """Get the global VideoRenderingService instance."""
    global _video_rendering_service
    if _video_rendering_service is None:
        _video_rendering_service = VideoRenderingService()
    return _video_rendering_service
