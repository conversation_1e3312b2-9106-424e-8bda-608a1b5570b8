"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Geist_Mono } from "next/font/google";

const geistMono = Geist_Mono({ subsets: ["latin"] });

interface ScrollingTextAnimationProps {
  text: string;
  wordsPerLine?: number;
  linesVisible?: number;
  animationSpeed?: number;
  className?: string;
  onComplete?: () => void;
}

export function ScrollingTextAnimation({
  text,
  wordsPerLine = 6,
  linesVisible = 5,
  animationSpeed = 2000,
  className,
  onComplete,
}: ScrollingTextAnimationProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const breakIntoLines = (text: string, wordsPerLine: number): string[] => {
    const words = text.split(" ").filter((word) => word.length > 0);
    const lines: string[] = [];

    for (let i = 0; i < words.length; i += wordsPerLine) {
      const line = words.slice(i, i + wordsPerLine).join(" ");
      lines.push(line);
    }

    return lines;
  };

  const lines = breakIntoLines(text, wordsPerLine);
  const centerIndex = Math.floor(linesVisible / 2);

  // Auto-advance animation
  useEffect(() => {
    if (currentIndex >= lines.length - centerIndex - 1) {
      if (currentIndex >= lines.length - centerIndex - 1 && onComplete) {
        onComplete();
      }
      return;
    }

    const timer = setTimeout(() => {
      setCurrentIndex((prev) => prev + 1);
    }, animationSpeed);

    return () => clearTimeout(timer);
  }, [currentIndex, lines.length, centerIndex, animationSpeed, onComplete]);

  const getVisibleLines = () => {
    const startIndex = Math.max(0, currentIndex - centerIndex);
    const endIndex = Math.min(lines.length, startIndex + linesVisible);

    return lines.slice(startIndex, endIndex).map((line, index) => ({
      text: line,
      originalIndex: startIndex + index,
      relativeIndex: index,
    }));
  };

  const visibleLines = getVisibleLines();

  const getLineStyle = (relativeIndex: number) => {
    const distanceFromCenter = Math.abs(relativeIndex - centerIndex);

    if (relativeIndex === centerIndex) {
      // Center line - fully focused
      return {
        opacity: 1,
        filter: "blur(0px)",
        scale: 1,
        y: 0,
      };
    } else {
      const opacity = Math.max(0.3, 1 - distanceFromCenter * 0.3);
      const blur = distanceFromCenter * 2;
      const scale = Math.max(0.9, 1 - distanceFromCenter * 0.05);

      return {
        opacity,
        filter: `blur(${blur}px)`,
        scale,
        y: 0,
      };
    }
  };

  return (
    <div
      className={cn(
        "relative h-[600px] bg-transparent flex flex-col justify-center items-center overflow-hidden",
        className
      )}
    >
      <div className="relative w-full max-w-xl">
        <AnimatePresence mode="popLayout">
          {visibleLines.map((line, index) => (
            <motion.div
              key={`${line.originalIndex}-${line.text.substring(0, 10)}`}
              className={cn(
                "absolute w-full text-start font-medium leading-tight tracking-tight",
                geistMono.className
              )}
              initial={{
                opacity: 0,
                y: 50,
                filter: "blur(10px)",
                scale: 0.9,
              }}
              animate={{
                ...getLineStyle(index),
                y: (index - centerIndex) * 80,
              }}
              exit={{
                opacity: 0,
                y: -50,
                filter: "blur(10px)",
                scale: 0.9,
              }}
              transition={{
                duration: 0.8,
                ease: [0.25, 0.46, 0.45, 0.94],
                opacity: { duration: 0.6 },
                filter: { duration: 0.6 },
                scale: { duration: 0.6 },
              }}
              style={{
                top: "50%",
                transform: "translateY(-50%)",
                textShadow: "0 0 10px rgba(255, 255, 255, 0.5)",
              }}
            >
              {line.text}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}
