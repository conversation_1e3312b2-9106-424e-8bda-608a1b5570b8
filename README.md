# ✨ ClarifAI – Visualize. Understand. Learn. 🚀

ClarifAI is an AI-powered platform that transforms **natural language prompts** into **stunning 2D/3D animations** for clearer understanding, deeper learning, and more effective communication. Whether you're a student, educator, content creator, or startup founder — ClarifAI helps you turn abstract ideas into **visually engaging animated videos**, instantly.

## 🎯 Why ClarifAI?

Understanding complex ideas shouldn't be hard. With ClarifAI, all you need to do is *describe what you're thinking*, and we’ll bring it to life — through AI-generated animations powered by **LLMs** and **Manim**.

---

## 💡 Key Features

- 🧠 **AI to Animation**  
  Convert plain English prompts into high-quality 2D or 3D animations using Manim.

- 📝 **AI-Generated Quizzes**  
  Reinforce learning with auto-generated quizzes tailored to each animation.

- 📊 **Concept Understanding Analysis**  
  Get feedback on your clarity of understanding with AI-based performance analytics.

- 📽️ **Video Library & Editor**  
  Create, play, edit, and manage all your animated videos in one organized place.

- 🌐 **Social Sharing**  
  Share your animations across platforms — be it with your team, classmates, or the world.

---

## 🧩 How It Works

1. **Input Prompt**  
   Users enter a concept or topic in natural language.

2. **AI Interpretation**  
   The prompt is processed via LLMs like Gemini or LLaMA, converting it into structured Manim code.

3. **Video Generation**  
   The Manim code is executed via Python in the backend to produce an animation.

4. **Quiz + Feedback (Optional)**  
   ClarifAI generates concept-based quizzes and evaluates user understanding.

5. **User Interaction**  
   Users can view, edit, save, delete, and share the generated videos.

---

## 🛠️ Tech Stack

- **Frontend:** Next.js, Tailwind CSS, TypeScript  
- **Backend:** Python, Manim, FastAPI  
- **AI:** Gemini / LLaMA (LLMs), Prompt Engineering  
- **Rendering Queue:** Python Multiprocessing, Custom Job Scheduling  
- **Storage & Video Management:** Local/Cloud Storage (extensible)  
- **Deployment:** Vercel (Frontend), Localhost/Cloud VM (Backend Rendering)

---

## ⚙️ Core Modules

- `PromptHandler`: Converts user prompt to structured AI requests.
- `LLMOrchestrator`: Handles prompt-to-code conversion and quiz generation.
- `RenderQueue`: Manages batch processing and rendering via message queues.
- `VideoManager`: Allows create/play/edit/delete of video assets.
- `AnalysisEngine`: Evaluates quiz answers and gives personalized feedback.

---

## 💻 Installation (Dev Environment)

> ⚠️ Animation rendering currently requires a local Python/Manim setup.

```bash
# Clone the repo
git clone https://github.com/your-username/clarifai.git
cd clarifai

# Frontend setup
cd frontend
bun i
bun run dev

# Backend setup
cd ../backend
pip install -r requirements.txt
python test.py
```

---

## 🧑‍💻 Author

- **Arghya Ghosh**
- **Anish Biswas**
- **Subhadeep Roy**
- **Suchismita Bose**


*Made with 💚 & 🐍*
