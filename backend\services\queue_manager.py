import logging
import queue
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional
import os
from dotenv import load_dotenv

from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)
from models.job_models import (
    JobRequest,
    JobStatusTracker,
    JobStatus,
    JobType,
    JobResult,
    QueueStats,
    RenderJobData,
    BatchRenderJobData,
    TopicRenderJobData,
)

logger = logging.getLogger(__name__)


class QueueManager:
    """Manages job queuing and status tracking using Python's built-in queue."""

    def __init__(self):
        self.config = get_queue_config()

        # Thread-safe job queue
        self._job_queue = queue.Queue()

        # In-memory job status tracking
        self._job_statuses: Dict[str, JobStatusTracker] = {}
        self._job_statuses_lock = threading.Lock()

        # Completed jobs tracking to prevent re-processing
        self._completed_jobs: set = set()
        self._completed_jobs_lock = threading.Lock()

        # Worker thread management
        self._worker_threads: List[threading.Thread] = []
        self._shutdown_event = threading.Event()

        # Initialize worker threads
        self._initialize_workers()

    def _initialize_workers(self):
        """Initialize worker threads for job processing."""
        try:
            for i in range(self.config.max_workers):
                worker_thread = threading.Thread(
                    target=self._worker_loop,
                    name=f"JobWorker-{i+1}",
                    daemon=True
                )
                worker_thread.start()
                self._worker_threads.append(worker_thread)

            logger.info(f"Initialized {self.config.max_workers} worker threads")
        except Exception as e:
            logger.error(f"Failed to initialize worker threads: {e}")
            raise

    def _worker_loop(self):
        """Main worker loop for processing jobs."""
        while not self._shutdown_event.is_set():
            try:
                # Get job from queue with timeout
                job_request = self._job_queue.get(timeout=self.config.queue_timeout)

                if job_request is None:  # Shutdown signal
                    break

                # Check if job is already completed
                with self._completed_jobs_lock:
                    if job_request.job_id in self._completed_jobs:
                        logger.info(f"Job {job_request.job_id} already completed, skipping")
                        self._job_queue.task_done()
                        continue

                # Process the job
                self._process_job_request(job_request)
                self._job_queue.task_done()

            except queue.Empty:
                # Timeout occurred, continue loop
                continue
            except Exception as e:
                logger.error(f"Worker error: {e}")
                if 'job_request' in locals():
                    self._job_queue.task_done()

    def queue_render_job(
        self,
        script: str,
        scene_name: Optional[str] = None,
        priority: int = 0,
        entry_id: Optional[str] = None,
    ) -> str:
        """Queue a single render job."""
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=RenderJobData(
                script=script, scene_name=scene_name, entry_id=entry_id
            ),
            max_retries=self.config.max_retries,
            timeout_seconds=self.config.job_timeout_seconds,
        )

        return self._queue_job(job_request, priority)

    def queue_batch_render_job(
        self,
        scripts: List[RenderJobData],
        priority: int = 0,
        entry_id: Optional[str] = None,
    ) -> str:
        """Queue a batch render job."""
        job_request = JobRequest(
            job_type=JobType.BATCH_RENDER,
            batch_render_data=BatchRenderJobData(scripts=scripts, entry_id=entry_id),
            max_retries=self.config.max_retries,
            timeout_seconds=self.config.job_timeout_seconds,
        )

        return self._queue_job(job_request, priority)

    def queue_topic_render_job(
        self,
        topic_render_data: TopicRenderJobData,
        priority: int = 0,
    ) -> str:
        """Queue a topic render job."""
        job_request = JobRequest(
            job_type=JobType.TOPIC_RENDER,
            topic_render_data=topic_render_data,
            max_retries=self.config.max_retries,
            timeout_seconds=self.config.job_timeout_seconds,
        )

        return self._queue_job(job_request, priority)

    def _queue_job(self, job_request: JobRequest, priority: int = 0) -> str:
        """Internal method to queue a job."""
        try:
            # Check if job is already completed
            with self._completed_jobs_lock:
                if job_request.job_id in self._completed_jobs:
                    logger.info(f"Job {job_request.job_id} already completed, not queuing")
                    return job_request.job_id

            # Calculate queue position
            queue_position = self._job_queue.qsize() + 1

            # Track job status
            job_status = JobStatusTracker(
                job_id=job_request.job_id,
                job_type=job_request.job_type,
                status=JobStatus.QUEUED,
                created_at=job_request.created_at,
                max_retries=job_request.max_retries,
                queue_position=queue_position,
            )

            with self._job_statuses_lock:
                self._job_statuses[job_request.job_id] = job_status

            # Add job to queue
            self._job_queue.put(job_request)

            logger.info(f"Job {job_request.job_id} queued successfully at position {queue_position}")
            return job_request.job_id

        except Exception as e:
            logger.error(f"Failed to queue job {job_request.job_id}: {e}")
            raise

    def _process_job_request(self, job_request: JobRequest):
        """Process a job request using the job processor."""
        try:
            # Import here to avoid circular imports
            from workers.job_processor import get_job_processor

            job_processor = get_job_processor()

            # Update job status to processing
            self.update_job_status(job_request.job_id, JobStatus.PROCESSING)

            # Process the job
            result = job_processor.process_job(job_request)

            # Mark job as completed
            with self._completed_jobs_lock:
                self._completed_jobs.add(job_request.job_id)

            # Update job status to completed
            self.update_job_status(job_request.job_id, JobStatus.COMPLETED, result=result)

            logger.info(f"Job {job_request.job_id} completed successfully")

        except Exception as e:
            logger.error(f"Job {job_request.job_id} failed: {e}")

            # Create failed result
            result = JobResult(
                job_id=job_request.job_id,
                success=False,
                error_message=str(e),
                completed_at=datetime.now(timezone.utc),
            )

            # Update job status to failed
            self.update_job_status(
                job_request.job_id,
                JobStatus.FAILED,
                result=result,
                error_message=str(e),
            )

    def get_job_queue_position(self, job_id: str) -> Optional[int]:
        """
        Get the position of a job in the queue.

        Args:
            job_id: The job ID to find

        Returns:
            Position in queue (1-based) or None if not found
        """
        try:
            # Get all queued jobs and find position
            queued_jobs = [
                job for job in self._job_statuses.values()
                if job.status == JobStatus.QUEUED
            ]

            # Sort by creation time to get queue order
            queued_jobs.sort(key=lambda x: x.created_at)

            # Find position (1-based)
            for position, job in enumerate(queued_jobs, 1):
                if job.job_id == job_id:
                    return position

            return None

        except Exception as e:
            logger.error(f"Failed to get queue position for job {job_id}: {e}")
            return None

    def get_job_status(self, job_id: str) -> Optional[JobStatusTracker]:
        """Get the status of a specific job."""
        with self._job_statuses_lock:
            return self._job_statuses.get(job_id)

    def update_job_status(
        self,
        job_id: str,
        status: JobStatus,
        result: Optional[JobResult] = None,
        error_message: Optional[str] = None,
    ):
        """Update job status."""
        with self._job_statuses_lock:
            if job_id not in self._job_statuses:
                logger.warning(f"Attempted to update unknown job: {job_id}")
                return

            job_status = self._job_statuses[job_id]
            job_status.status = status

            if status == JobStatus.PROCESSING and not job_status.started_at:
                job_status.started_at = datetime.now(timezone.utc)

            if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                job_status.completed_at = datetime.now(timezone.utc)

            if result:
                job_status.result = result

            if error_message:
                job_status.last_error = error_message
                job_status.error_count += 1

            if status == JobStatus.FAILED:
                job_status.current_retry += 1

        logger.info(f"Job {job_id} status updated to {status}")

    def get_queue_stats(self) -> QueueStats:
        """Get statistics for the job queue."""
        stats = QueueStats()

        with self._job_statuses_lock:
            for job_status in self._job_statuses.values():
                stats.total_jobs += 1

                if job_status.status == JobStatus.QUEUED:
                    stats.queued_jobs += 1
                elif job_status.status == JobStatus.PROCESSING:
                    stats.processing_jobs += 1
                elif job_status.status == JobStatus.COMPLETED:
                    stats.completed_jobs += 1
                elif job_status.status == JobStatus.FAILED:
                    stats.failed_jobs += 1
                elif job_status.status == JobStatus.CANCELLED:
                    stats.cancelled_jobs += 1

        return stats

    def list_jobs(
        self,
        status_filter: Optional[JobStatus] = None,
        job_type_filter: Optional[JobType] = None,
        limit: int = 100,
    ) -> List[JobStatusTracker]:
        """List jobs with optional filtering."""
        with self._job_statuses_lock:
            jobs = list(self._job_statuses.values())

        if status_filter:
            jobs = [job for job in jobs if job.status == status_filter]

        if job_type_filter:
            jobs = [job for job in jobs if job.job_type == job_type_filter]

        # Sort by creation time (newest first)
        jobs.sort(key=lambda x: x.created_at, reverse=True)

        return jobs[:limit]

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job if it's not already processing or completed."""
        job_status = self.get_job_status(job_id)
        if not job_status:
            return False

        if job_status.status in [JobStatus.COMPLETED, JobStatus.PROCESSING]:
            return False

        self.update_job_status(job_id, JobStatus.CANCELLED)
        return True

    def retry_failed_job(self, job_id: str) -> bool:
        """Retry a failed job if it can be retried."""
        job_status = self.get_job_status(job_id)
        if not job_status or not job_status.can_retry():
            return False

        try:
            # Reset job status for retry
            with self._job_statuses_lock:
                job_status.status = JobStatus.QUEUED
                job_status.last_error = None

            logger.info(
                f"Job {job_id} marked for retry (attempt {job_status.current_retry + 1})"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to retry job {job_id}: {e}")
            return False

    def handle_dead_letter_job(self, job_id: str, final_error: str):
        """Handle a job that has exhausted all retries."""
        job_status = self.get_job_status(job_id)
        if not job_status:
            return

        logger.error(
            f"Job {job_id} moved to dead letter queue after {job_status.current_retry} retries. Final error: {final_error}"
        )

        # Update job status to failed with final error
        self.update_job_status(
            job_id, JobStatus.FAILED, error_message=f"Dead letter: {final_error}"
        )

        # In a production system, you might want to:
        # 1. Store the job in a separate dead letter queue
        # 2. Send notifications to administrators
        # 3. Log to external monitoring systems

    def cleanup_completed_jobs(self, older_than_hours: int = 24):
        """Clean up completed jobs older than specified hours."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=older_than_hours)
        jobs_to_remove = []

        with self._job_statuses_lock:
            for job_id, job_status in self._job_statuses.items():
                if (
                    job_status.status == JobStatus.COMPLETED
                    and job_status.completed_at
                    and job_status.completed_at < cutoff_time
                ):
                    jobs_to_remove.append(job_id)

            for job_id in jobs_to_remove:
                del self._job_statuses[job_id]
                logger.info(f"Cleaned up completed job: {job_id}")

        # Also clean up completed jobs from the completed set
        with self._completed_jobs_lock:
            # We can't easily determine which completed jobs are old without additional tracking
            # For now, we'll keep the completed jobs set as is
            pass

        return len(jobs_to_remove)

    def get_failed_jobs(self) -> List[JobStatusTracker]:
        """Get all failed jobs that can potentially be retried."""
        with self._job_statuses_lock:
            return [
                job
                for job in self._job_statuses.values()
                if job.status == JobStatus.FAILED and job.can_retry()
            ]

    def shutdown(self):
        """Shutdown the queue manager and worker threads."""
        logger.info("Shutting down queue manager...")

        # Signal shutdown to worker threads
        self._shutdown_event.set()

        # Add None items to queue to wake up workers
        for _ in self._worker_threads:
            self._job_queue.put(None)

        # Wait for worker threads to finish
        for thread in self._worker_threads:
            thread.join(timeout=5.0)

        logger.info("Queue manager shutdown complete")


# Global queue manager instance
_queue_manager: Optional[QueueManager] = None


def get_queue_manager() -> QueueManager:
    """Get the global queue manager instance."""
    global _queue_manager
    if _queue_manager is None:
        _queue_manager = QueueManager()
    return _queue_manager
